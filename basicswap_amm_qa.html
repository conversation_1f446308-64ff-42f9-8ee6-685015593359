<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BasicSwap AMM Q&A</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        
        .qa-section {
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
            padding-left: 20px;
        }
        
        .question {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
        
        .answer {
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .code-snippet {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        
        ul, ol {
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        .toc {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .toc h2 {
            margin-top: 0;
            color: #495057;
        }
        
        .toc a {
            color: #007bff;
            text-decoration: none;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BasicSwap AMM (Automated Market Making) Q&A</h1>
        
        <div class="toc">
            <h2>Table of Contents</h2>
            <ol>
                <li><a href="#price-settlement">Price Agreement and Settlement</a></li>
                <li><a href="#offers-vs-orders">Why "Offers" Instead of "Orders"?</a></li>
                <li><a href="#not-clob">Why Not a Traditional CLOB?</a></li>
                <li><a href="#fee-fields">Fee Fields</a></li>
                <li><a href="#offer-rules">Offer Rules and Balance Behavior</a></li>
                <li><a href="#expiry-times">Expiry Times and Clock Reference</a></li>
                <li><a href="#price-adjustments">Price Adjustments in AMM</a></li>
                <li><a href="#bids-vs-offers">Bids vs Offers Structure</a></li>
                <li><a href="#watched-outputs">Watched Outputs</a></li>
                <li><a href="#wallet-policies">Wallet Policies for Unconfirmed Balances</a></li>
                <li><a href="#reputation">Reputation System</a></li>
                <li><a href="#gui-dropdown">GUI Dropdown Issue</a></li>
                <li><a href="#swap-types">Swap Type Selection</a></li>
                <li><a href="#rate-fields">Rate Fields Explanation</a></li>
                <li><a href="#smsg-identity">SMSG Address/Identity</a></li>
                <li><a href="#conflicting-offers">Conflicting Offers and Balance</a></li>
                <li><a href="#offer-size-precision">Offer Size Adjustment Precision</a></li>
            </ol>
        </div>

        <div id="price-settlement" class="qa-section">
            <div class="question">Q: At what stage is price agreed and at what stage is settlement guaranteed? What is the latest stage at which an agreed trade is still able to fail to settle and what is that signal?</div>
            <div class="answer">
                <p><strong>Price Agreement Stage:</strong> Price is agreed upon when a bid is accepted by the offer maker. This happens during the bid acceptance phase, before any blockchain transactions are initiated.</p>
                
                <p><strong>Settlement Guarantee:</strong> Settlement is guaranteed once both parties have committed their funds to the blockchain lock transactions (ITX and PTX in adaptor signature swaps, or initiate/participate transactions in secret hash swaps).</p>
                
                <p><strong>Latest Failure Point:</strong> The latest stage where an agreed trade can still fail to settle is during the lock transaction confirmation phase. The signals for this failure would be:</p>
                <ul>
                    <li>Lock transactions not confirming within the timeout period</li>
                    <li>Insufficient funds in wallets when attempting to create lock transactions</li>
                    <li>Network connectivity issues preventing transaction broadcast</li>
                </ul>
            </div>
        </div>

        <div id="offers-vs-orders" class="qa-section">
            <div class="question">Q: Why are they called offers and not orders?</div>
            <div class="answer">
                <p>They're called "offers" because BasicSwap uses a peer-to-peer messaging system (SMSG) rather than a traditional centralized order book. An "offer" is broadcast to the network via SMSG messages, and interested parties respond with "bids." This terminology reflects the decentralized, message-based nature of the system.</p>
            </div>
        </div>

        <div id="not-clob" class="qa-section">
            <div class="question">Q: Why isn't it a replica of a normal CLOB? (Limit orders + market orders)</div>
            <div class="answer">
                <p>BasicSwap isn't a replica of a normal Central Limit Order Book (CLOB) because:</p>
                <ol>
                    <li><strong>Decentralized Architecture:</strong> No central server maintains the order book</li>
                    <li><strong>P2P Messaging:</strong> Uses SMSG (Secure Messaging) for communication</li>
                    <li><strong>Cross-Chain Focus:</strong> Designed specifically for atomic swaps between different blockchains</li>
                    <li><strong>Privacy:</strong> SMSG provides better privacy than traditional order books</li>
                    <li><strong>Censorship Resistance:</strong> No central authority can block trades</li>
                </ol>
            </div>
        </div>

        <div id="fee-fields" class="qa-section">
            <div class="question">Q: What are the fee fields about?</div>
            <div class="answer">
                <p>The fee fields control:</p>
                <ul>
                    <li><strong>Transaction fees</strong> for blockchain operations</li>
                    <li><strong>Network fees</strong> for SMSG message transmission</li>
                    <li><strong>Fee estimation</strong> for different confirmation targets</li>
                    <li><strong>Subfee options</strong> for deducting fees from the sent amount</li>
                </ul>
            </div>
        </div>

        <div id="offer-rules" class="qa-section">
            <div class="question">Q: What are the rules of an offer - why insatiable quantity on manual mode and limited balance in AMM mode - why does it work like that?</div>
            <div class="answer">
                <p><strong>Manual Mode vs AMM Mode:</strong></p>
                <ul>
                    <li><strong>Manual Mode:</strong> Offers have "insatiable quantity" because users manually control when to create/revoke offers</li>
                    <li><strong>AMM Mode:</strong> Limited by wallet balance because the AMM automatically manages offers based on available funds to prevent over-commitment</li>
                </ul>
                
                <p><strong>Amount Step (Offer Size Increment):</strong> This is a privacy feature that prevents revealing exact wallet balances. Instead of offering your entire balance, offers are created in stepped amounts.</p>
                
                <div class="code-snippet">
"amount_step": REQUIRED - Offer size increment for privacy. Must be between 0.001 and "amount".
              This prevents revealing exact wallet balance by creating stepped offers.
              Example: 150 LTC balance, 100 LTC offer, 5 LTC increment, 76 LTC min balance = creates 70 LTC offer.
              Without this, partial fills would reveal your exact remaining balance.
                </div>
            </div>
        </div>

        <div id="expiry-times" class="qa-section">
            <div class="question">Q: How are expiry times judged for offer validity (against which clock?)</div>
            <div class="answer">
                <p><strong>Offer Validity:</strong> Expiry times are judged against the local system clock when creating offers, but the SMSG network has its own TTL (Time To Live) mechanism.</p>
                
                <div class="code-snippet">
SMSG_MIN_TTL = 60 * 60  # Minimum 1 hour
SMSG_BUCKET_LEN = 60 * 60
                </div>
                
                <p><strong>SMSG Expiration:</strong> Yes, this is due to SMSG network limitations where nodes automatically prune old messages based on time and storage constraints. BasicSwap offers have a set expiration time due to limitations of the SMSG P2P network - this is the expectation that data will be wiped by nodes after defaults like "2 weeks" or "1GB" (whichever comes first).</p>
            </div>
        </div>

        <div id="price-adjustments" class="qa-section">
            <div class="question">Q: Price adjustments in the AMM - is this adjusted only with each reposting of an offer? If so, it is distinct from the 'allow bids at varying prices option', right?</div>
            <div class="answer">
                <p><strong>Rate Adjustments:</strong> Yes, price adjustments happen only when offers are reposted. The AMM checks market conditions and adjusts rates based on the <code>ratetweakpercent</code> and <code>adjust_rates_based_on_market</code> settings.</p>
                
                <p><strong>"Allow Bids at Varying Prices":</strong> This is distinct from rate adjustments. It allows bidders to submit bids at different rates than the posted offer rate, providing more flexibility in negotiations.</p>
            </div>
        </div>

        <div id="bids-vs-offers" class="qa-section">
            <div class="question">Q: What's the point of that 'allow bids at other prices'? Is that because bids are tied to specific offers, they aren't actually just resting orders in a book sorted by price-time?</div>
            <div class="answer">
                <p><strong>Why Bids Aren't Just Resting Orders:</strong> Bids are tied to specific offers because BasicSwap uses a request-response model rather than a traditional order book. This ensures:</p>
                <ul>
                    <li>Direct negotiation between parties</li>
                    <li>Better privacy (no public order book)</li>
                    <li>Atomic swap compatibility</li>
                </ul>
                
                <p>The "allow bids at other prices" feature enables price discovery and negotiation within the P2P framework.</p>
            </div>
        </div>

        <div id="watched-outputs" class="qa-section">
            <div class="question">Q: Watched outputs - is this just a way to monitor settlement, or is cheating possible too?</div>
            <div class="answer">
                <p><strong>Purpose:</strong> Watched outputs monitor settlement by tracking when specific UTXOs (transaction outputs) get spent on the blockchain. This is primarily for monitoring settlement completion, not preventing cheating.</p>
                
                <div class="highlight">
                    Watch when your outputs involved in a swap get spent on the blockchain.
                </div>
                
                <p>The atomic swap protocols themselves prevent cheating through cryptographic guarantees, not through monitoring.</p>
            </div>
        </div>

        <div id="wallet-policies" class="qa-section">
            <div class="question">Q: What are the wallet policies on spending unconfirmed balances?</div>
            <div class="answer">
                <p><strong>Unconfirmed Balance Handling:</strong> BasicSwap includes both unconfirmed and immature balances in "pending" calculations but generally requires confirmed balances for swap operations to ensure security.</p>
                
                <div class="code-snippet">
pending: int = 0
if "unconfirmed" in w and float(w["unconfirmed"]) > 0.0:
    pending += ci.make_int(w["unconfirmed"])
if "immature" in w and float(w["immature"]) > 0.0:
    pending += ci.make_int(w["immature"])
                </div>
            </div>
        </div>

        <div id="reputation" class="qa-section">
            <div class="question">Q: Do I have a reputation score? Can I (I don't want one, just asking if that is an aspect to consider)?</div>
            <div class="answer">
                <p><strong>No Reputation Scoring:</strong> BasicSwap does not implement a reputation system. It relies on cryptographic proofs and atomic swap protocols for security rather than trust-based reputation. This aligns with the decentralized, trustless nature of the system.</p>
            </div>
        </div>

        <div id="gui-dropdown" class="qa-section">
            <div class="question">Q: Really hard to select the drop down list check boxes in the network orders page GUI bug (safari and Firefox)</div>
            <div class="answer">
                <p><strong>Dropdown Checkbox Selection:</strong> The issue you mentioned with dropdown checkboxes being hard to select appears to be a CSS/JavaScript interaction problem. The code shows proper event handling for checkboxes, but there may be z-index or click event propagation issues affecting usability in Safari and Firefox.</p>
                
                <p>This is a known UI/UX issue that would benefit from CSS fixes to improve click target areas and event handling.</p>
            </div>
        </div>

        <div id="swap-types" class="qa-section">
            <div class="question">Q: Swap Type Selection: Choose between Secret Hash or Adaptor Signature swap types. IIUC XMR needs adaptor sigs (Scriptless scripts). Why am I given a choice sometimes? If a chain supports both, what are the advantages of one versus the other?</div>
            <div class="answer">
                <p><strong>XMR and Adaptor Signatures:</strong> XMR requires adaptor signatures because it doesn't support scripts. The choice between secret hash and adaptor signature swaps depends on the coins involved:</p>
                
                <div class="code-snippet">
adaptor_sig_only_coins: ['6', '9', '8', '7', '13', '18', '17'],
secret_hash_only_coins: ['11', '12'],
                </div>
                
                <p><strong>Advantages of Adaptor Signatures:</strong></p>
                <ul>
                    <li>Smaller data footprint</li>
                    <li>Better privacy (no hash preimage revelation)</li>
                    <li>Works with scriptless coins like Monero</li>
                </ul>
                
                <p>When both are supported, adaptor signatures are generally preferred for their efficiency and privacy benefits.</p>
            </div>
        </div>

        <div id="rate-fields" class="qa-section">
            <div class="question">Q: Why is there a 'Minimum Rate' field and a 'Rate tweak percent' field?</div>
            <div class="answer">
                <p><strong>Minimum Rate vs Rate Tweak Percent:</strong></p>
                <ul>
                    <li><strong>Minimum Rate:</strong> The absolute floor below which offers won't be created</li>
                    <li><strong>Rate Tweak Percent:</strong> A percentage adjustment applied to market rates (can be positive or negative)</li>
                </ul>
                
                <p>The tweak percent allows dynamic adjustment based on market conditions, while the minimum rate provides a safety net to prevent offers at unacceptable rates.</p>
            </div>
        </div>

        <div id="smsg-identity" class="qa-section">
            <div class="question">Q: SMSG address/identity: Public field? Can I write "Cryptonomy" for example? What can be gleaned about me from the auto value?</div>
            <div class="answer">
                <p><strong>SMSG Address:</strong> This is a public field that can contain custom text like "Cryptonomy". The auto-generated value reveals your public key but doesn't compromise privacy beyond normal blockchain analysis.</p>
                
                <p>The identity field is primarily for user convenience and doesn't affect the security or privacy of the atomic swap process.</p>
            </div>
        </div>

        <div id="conflicting-offers" class="qa-section">
            <div class="question">Q: What happens with conflicting offers when coins available falls short in wallet to satisfy incoming bids?</div>
            <div class="answer">
                <p><strong>Balance Conflicts:</strong> When wallet balance falls short, the AMM automatically revokes offers to prevent failed swaps. Manual offers require user intervention.</p>
                
                <div class="code-snippet">
if wallet_balance <= float(offer_template["min_coin_from_amt"]):
    offer_id = offer["offer_id"]
    print("Revoking offer {}, wallet from balance below minimum".format(offer_id))
    result = read_json_api(f"revokeoffer/{offer_id}")
                </div>
                
                <p>This prevents the creation of offers that cannot be fulfilled.</p>
            </div>
        </div>

        <div id="offer-size-precision" class="qa-section">
            <div class="question">Q: Offer size adjustment precision (aka Offer size Increment): "The precision of offer size adjustments after a partial fill. The precision of offer size adjustments after a partial fill." <- why not just 'the remainder of the original offer amount'?</div>
            <div class="answer">
                <p><strong>Why Not Just Remainder:</strong> The offer size increment serves as a privacy feature rather than just handling remainders. It prevents revealing exact wallet balances by creating offers in predetermined steps.</p>
                
                <p>This approach ensures that even after partial fills, the remaining offer amounts don't reveal sensitive information about the user's total holdings.</p>
                
                <div class="highlight">
                    The precision controls how offers are broken down to maintain privacy while allowing flexible trading amounts.
                </div>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background-color: #e9ecef; border-radius: 5px; text-align: center;">
            <p><strong>Note:</strong> This Q&A is based on the BasicSwap codebase analysis and covers the AMM functionality as implemented. For the most current information, always refer to the official BasicSwap documentation.</p>
        </div>
    </div>
</body>
</html>
