#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Copyright (c) 2025 crz
# Distributed under the MIT software license, see the accompanying
# file LICENSE or http://www.opensource.org/licenses/mit-license.php.

"""
Test external address functionality for swap destinations.
"""

import os
import json
import logging
import unittest

from basicswap.basicswap import BasicSwap
from basicswap.basicswap_util import SwapTypes
from basicswap.util import COIN
from basicswap.chainparams import Coins

from tests.basicswap.common import (
    read_json_api,
    TEST_HTTP_PORT,
    BASE_PORT,
    BASE_RPC_PORT,
    prepareOtherDir,
    make_rpc_func,
)
from tests.basicswap.test_xmr import BaseTest, test_delay_event


class Test(BaseTest):
    __test__ = True

    @classmethod
    def setUpClass(cls):
        super(Test, cls).setUpClass()

    def test_01_external_address_validation(self):
        """Test external address validation in API"""
        logging.info("---------- Test external address validation")
        
        swap_clients = self.swap_clients
        ci_from = swap_clients[0].ci(Coins.PART)
        ci_to = swap_clients[0].ci(Coins.XMR)
        
        # Test valid address
        valid_addr = ci_to.getNewAddress("")
        self.assertTrue(ci_to.isValidAddress(valid_addr))
        
        # Test invalid address
        invalid_addr = "invalid_address_123"
        self.assertFalse(ci_to.isValidAddress(invalid_addr))

    def test_02_external_address_api(self):
        """Test external address parameter in bid creation API"""
        logging.info("---------- Test external address API")
        
        swap_clients = self.swap_clients
        
        # Create an offer
        offer_id = swap_clients[0].postOffer(
            Coins.PART,
            Coins.XMR,
            100 * COIN,
            0.11 * 10**12,  # XMR uses 12 decimals
            100 * COIN,
            SwapTypes.XMR_SWAP,
        )
        
        # Get a valid external address
        external_addr = swap_clients[1].ci(Coins.XMR).getNewAddress("")
        
        # Test bid with external address via API
        post_json = {
            "offer_id": offer_id.hex(),
            "amount_from": "100.0",
            "addr_to": external_addr
        }
        
        try:
            json_rv = read_json_api(TEST_HTTP_PORT + 1, "bids/new", post_json)
            self.assertTrue("bid_id" in json_rv)
            bid_id = bytes.fromhex(json_rv["bid_id"])
            
            # Verify the bid has the external address set
            bid = swap_clients[1].getBid(bid_id)
            self.assertEqual(bid.withdraw_to_addr, external_addr)
            
        except Exception as e:
            self.fail(f"Failed to create bid with external address: {e}")

    def test_03_external_address_invalid_api(self):
        """Test external address validation in API"""
        logging.info("---------- Test invalid external address API")
        
        swap_clients = self.swap_clients
        
        # Create an offer
        offer_id = swap_clients[0].postOffer(
            Coins.PART,
            Coins.XMR,
            100 * COIN,
            0.11 * 10**12,
            100 * COIN,
            SwapTypes.XMR_SWAP,
        )
        
        # Test bid with invalid external address
        post_json = {
            "offer_id": offer_id.hex(),
            "amount_from": "100.0",
            "addr_to": "invalid_address_123"
        }
        
        try:
            json_rv = read_json_api(TEST_HTTP_PORT + 1, "bids/new", post_json)
            self.fail("Should have failed with invalid address")
        except Exception as e:
            # Should fail with validation error
            self.assertIn("Invalid destination address", str(e))

    def test_04_external_address_ltc_mweb(self):
        """Test external address with LTC MWEB support"""
        logging.info("---------- Test LTC MWEB external address")
        
        swap_clients = self.swap_clients
        
        # Skip if LTC not available
        if Coins.LTC not in swap_clients[0].coin_clients:
            self.skipTest("LTC not available")
        
        ci_ltc = swap_clients[0].ci(Coins.LTC)
        
        # Test regular LTC address
        regular_addr = ci_ltc.getNewAddress(False)  # Non-segwit
        self.assertTrue(ci_ltc.isValidAddress(regular_addr))
        
        # Test segwit address
        segwit_addr = ci_ltc.getNewAddress(True)  # Segwit
        self.assertTrue(ci_ltc.isValidAddress(segwit_addr))

    def test_05_external_address_empty_default(self):
        """Test that empty external address defaults to internal wallet"""
        logging.info("---------- Test empty external address defaults")

        swap_clients = self.swap_clients

        # Create an offer
        offer_id = swap_clients[0].postOffer(
            Coins.PART,
            Coins.XMR,
            100 * COIN,
            0.11 * 10**12,
            100 * COIN,
            SwapTypes.XMR_SWAP,
        )

        # Test bid with empty external address
        post_json = {
            "offer_id": offer_id.hex(),
            "amount_from": "100.0",
            "addr_to": ""  # Empty address
        }

        try:
            json_rv = read_json_api(TEST_HTTP_PORT + 1, "bids/new", post_json)
            self.assertTrue("bid_id" in json_rv)
            bid_id = bytes.fromhex(json_rv["bid_id"])

            # Verify the bid has no external address set (should be None)
            bid = swap_clients[1].getBid(bid_id)
            self.assertIsNone(bid.withdraw_to_addr)

        except Exception as e:
            self.fail(f"Failed to create bid with empty external address: {e}")

    def test_06_amm_external_address(self):
        """Test AMM external address functionality"""
        logging.info("---------- Test AMM external address")

        swap_clients = self.swap_clients

        # Test AMM bid template with external address
        external_addr = swap_clients[1].ci(Coins.XMR).getNewAddress("")

        bid_template = {
            "coin_from": "Particl",
            "coin_to": "Monero",
            "amount": 1.0,
            "max_rate": 0.12,
            "destination_address": external_addr,
            "enabled": True
        }

        # Verify the template includes the external address
        self.assertEqual(bid_template["destination_address"], external_addr)

        # Test that "auto" is handled correctly
        bid_template_auto = {
            "coin_from": "Particl",
            "coin_to": "Monero",
            "amount": 1.0,
            "max_rate": 0.12,
            "destination_address": "auto",
            "enabled": True
        }

        self.assertEqual(bid_template_auto["destination_address"], "auto")

    def test_07_address_rotation(self):
        """Test automatic address rotation functionality"""
        logging.info("---------- Test address rotation")

        swap_clients = self.swap_clients

        # Test address usage detection
        ci_part = swap_clients[0].ci(Coins.PART)

        # Generate a new address
        new_addr = ci_part.getNewAddress(False)

        # Initially should be unused
        is_used_before = swap_clients[0].isAddressUsed(Coins.PART, new_addr)
        self.assertFalse(is_used_before, "New address should be unused")

        # Test getting unused address
        unused_addr = swap_clients[0].getUnusedReceiveAddress(Coins.PART)
        self.assertIsNotNone(unused_addr)
        self.assertTrue(ci_part.isValidAddress(unused_addr))

        # Test caching new address with rotation
        cached_addr = swap_clients[0].cacheNewAddressForCoin(Coins.PART)
        self.assertIsNotNone(cached_addr)
        self.assertTrue(ci_part.isValidAddress(cached_addr))

    def test_08_wallet_history(self):
        """Test wallet transaction history functionality"""
        logging.info("---------- Test wallet transaction history")

        swap_clients = self.swap_clients

        # Test getting transaction history for PART
        history = swap_clients[0].getWalletTransactionHistory(Coins.PART, 10)
        self.assertIsInstance(history, list)

        # Test getting transaction history for XMR
        if Coins.XMR in swap_clients[0].coin_clients:
            history_xmr = swap_clients[0].getWalletTransactionHistory(Coins.XMR, 10)
            self.assertIsInstance(history_xmr, list)

        # Each transaction should have required fields
        for tx in history[:5]:  # Check first 5 transactions
            self.assertIn('txid', tx)
            self.assertIn('type', tx)
            self.assertIn('amount', tx)
            self.assertIn('confirmations', tx)
            self.assertIn('timestamp', tx)


if __name__ == "__main__":
    unittest.main()
