<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notification Features</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        button { margin: 5px; padding: 10px 15px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .dropdown { position: relative; display: inline-block; }
        .dropdown-content { display: none; position: absolute; background-color: #f9f9f9; min-width: 300px; box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); z-index: 1; border-radius: 5px; }
        .dropdown:hover .dropdown-content { display: block; }
        #notification-history-dropdown { border: 1px solid #ddd; }
        .donation-test { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>BasicSwap Notification Features Test</h1>
    
    <div class="test-section">
        <h2>1. Notification History Test</h2>
        <p>Test the notification history functionality:</p>
        <button onclick="testCreateNotification()">Create Test Notification</button>
        <button onclick="testBalanceNotification()">Create Balance Change Notification</button>
        <button onclick="clearHistory()">Clear History</button>
        <button onclick="toggleNotificationDropdown()">Toggle Dropdown</button>

        <div class="dropdown">
            <button onclick="toggleNotificationDropdown()">View Notification History</button>
            <div id="notification-history-dropdown" class="dropdown-content">
                <div class="px-4 py-3 text-sm text-gray-500">No notifications yet</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>2. Donation Address Test</h2>
        <p>Test the donation address auto-fill functionality:</p>
        
        <div class="donation-test">
            <strong>Support BasicSwap Development!</strong>
            Consider donating Litecoin to help fund development.
            <button onclick="fillDonationAddress('ltc1qevlumv48nz2afl0re9ml4tdewc56svxq3egkyt', 'LTC')"
                    style="background: #007cba; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 0 5px;">
              Use LTC Address
            </button>
            <button onclick="fillDonationAddress('ltcmweb1qqt9rwznnxzkghv4s5wgtwxs0m0ry6n3atp95f47slppapxljde3xyqmdlnrc8ag7y2k354jzdc4pc4ks0kr43jehr77lngdecgh6689nn5mgv5yn', 'MWEB')"
                    style="background: #8b5cf6; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 0 5px;">
              Use MWEB Address
            </button>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
              Current type: <span id="donation-type-indicator">LTC</span>
            </div>
        </div>
        
        <div style="margin: 10px 0;">
            <label for="test-address">Test Address Field:</label><br>
            <input type="text" id="test-address" name="to_3" style="width: 400px; padding: 8px; margin: 5px 0;" placeholder="Address will be filled here">
        </div>
    </div>

    <div class="test-section">
        <h2>3. Test Results</h2>
        <div id="test-results" style="background: #f5f5f5; padding: 10px; border-radius: 3px; min-height: 100px;">
            Test results will appear here...
        </div>
    </div>

    <!-- Include the notification manager -->
    <script src="/static/js/modules/notification-manager.js"></script>
    
    <script>
        // Initialize notification manager
        if (window.NotificationManager) {
            window.NotificationManager.initialize({
                showBalanceChanges: true,
                showOutgoingTransactions: true
            });
        }

        function testCreateNotification() {
            if (window.NotificationManager) {
                window.NotificationManager.createToast(
                    'Test Notification', 
                    'success', 
                    { subtitle: 'This is a test notification' }
                );
                updateResults('✅ Test notification created');
            } else {
                updateResults('❌ NotificationManager not available');
            }
        }

        function testBalanceNotification() {
            if (window.NotificationManager) {
                window.NotificationManager.createToast(
                    '+0.5 LTC', 
                    'balance_change', 
                    { 
                        coinSymbol: 'LTC', 
                        subtitle: 'Incoming funds confirmed' 
                    }
                );
                updateResults('✅ Balance change notification created');
            } else {
                updateResults('❌ NotificationManager not available');
            }
        }

        function clearHistory() {
            if (window.NotificationManager) {
                window.NotificationManager.clearNotificationHistory();
                updateResults('✅ Notification history cleared');
            } else {
                updateResults('❌ NotificationManager not available');
            }
        }

        function fillDonationAddress(address, type) {
            const addressInput = document.querySelector('input[name^="to_"]');
            if (addressInput) {
                addressInput.value = address;
                addressInput.focus();

                // Update type indicator if it exists
                const typeIndicator = document.getElementById('donation-type-indicator');
                if (typeIndicator && type) {
                    typeIndicator.textContent = type;
                }

                updateResults('✅ ' + (type || 'Donation') + ' address filled: ' + address.substring(0, 20) + '...');
            } else {
                updateResults('❌ Address input field not found');
            }
        }

        function toggleNotificationDropdown() {
            const dropdown = document.getElementById('notification-history-dropdown');
            if (dropdown) {
                dropdown.classList.toggle('hidden');
                updateResults('🔔 Notification dropdown toggled');
            } else {
                updateResults('❌ Notification dropdown not found');
            }
        }

        function updateResults(message) {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        // Test initialization
        document.addEventListener('DOMContentLoaded', function() {
            updateResults('🚀 Test page loaded');
            
            if (window.NotificationManager) {
                updateResults('✅ NotificationManager loaded successfully');
                
                // Test history functionality
                const history = window.NotificationManager.getNotificationHistory();
                updateResults(`📊 Current history count: ${history.length}`);
            } else {
                updateResults('❌ NotificationManager not loaded');
            }
        });
    </script>
</body>
</html>
