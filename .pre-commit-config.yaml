repos:
  # Common hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
    - id: check-added-large-files
    - id: check-merge-conflict
      args: ["--assume-in-merge"]
    - id: check-yaml
    - id: detect-private-key
    - id: end-of-file-fixer
    - id: trailing-whitespace
      args: ["--markdown-linebreak-ext=md"]

  # Black - Python formatter
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
    - id: black
      exclude: (basicswap/contrib|basicswap/interface/contrib)/

  # Flake8 - Lint Python
  - repo: https://github.com/pycqa/flake8
    rev: 7.3.0
    hooks:
    - id: flake8
      args: ["--ignore=E203,E501,W503", "--exclude=basicswap/contrib,basicswap/interface/contrib,.eggs,.tox,bin/install_certifi.py"]

  # ESLint - Lint Javascript and fix issues where possible
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v9.30.1
    hooks:
    - id: eslint
      #args: ["--fix"]

  # djLint - Lint HTML
  #- repo: https://github.com/djlint/djlint
  #  rev: v1.36.4
  #  hooks:
  #  - id: djlint
