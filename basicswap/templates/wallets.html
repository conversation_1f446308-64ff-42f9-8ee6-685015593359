{% include 'header.html' %}
{% from 'style.html' import breadcrumb_line_svg, circular_arrows_svg, withdraw_svg, utxo_groups_svg, create_utxo_svg, lock_svg, eye_show_svg %}

<section class="py-3 px-4 mt-6">
  <div class="lg:container mx-auto">
   <div class="relative py-8 px-8 bg-coolGray-900 dark:bg-blue-500 rounded-md overflow-hidden">
      <img class="absolute z-10 left-4 top-4" src="/static/images/elements/dots-red.svg" alt="dots-red">
      <img class="absolute z-10 right-4 bottom-4" src="/static/images/elements/dots-red.svg" alt="dots-red">
      <img class="absolute h-64 left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 object-cover" src="/static/images/elements/wave.svg" alt="wave">
    <div class="relative z-20 flex flex-wrap items-center -m-3">
     <div class="w-full md:w-1/2 p-3 h-48">
      <h2 class="text-4xl font-bold text-white tracking-tighter">Wallets</h2>
      <div class="flex items-center">
        <h2 class="text-lg font-bold text-white tracking-tighter mr-2">Total Assets:</h2>
        <button id="hide-usd-amount-toggle" class="flex items-center justify-center p-1 focus:ring-0 focus:outline-none">{{ eye_show_svg | safe }}</button>
      </div>
      <div class="flex items-baseline mt-2">
        <div id="total-usd-value" class="text-5xl font-bold text-white"></div>
        <div id="usd-text" class="text-sm text-white ml-1">USD</div>
      </div>
        <div id="total-btc-value" class="text-sm text-white mt-2"></div>
     </div>
      <div class="w-full md:w-1/2 p-3 p-6 container flex flex-wrap items-center justify-end items-center mx-auto">
        <a class="rounded-full flex flex-wrap justify-center px-5 py-3 bg-blue-500 hover:bg-blue-600 font-medium text-sm text-white border dark:bg-gray-500 dark:hover:bg-gray-700 border-blue-500 rounded-md shadow-button focus:ring-0 focus:outline-none" href="/changepassword">{{ lock_svg | safe }}<span>Change/Set Password</span></a>
     </div>
    </div>
   </div>
  </div>
 </section>
 
 {% include 'inc_messages.html' %}

 <section class="py-4">
  <div class="container mx-auto">
   <div class="flex flex-wrap -m-4">
    {% for w in wallets %}
    {% if w.havedata %}
    {% if w.error %}<p>Error: {{ w.error }}</p>
    {% else %}
    <div class="w-full lg:w-1/3 p-4">
     <div class="bg-gray-50 rounded overflow-hidden dark:bg-gray-500">
      <div class="pt-6 px-6 mb-10 flex justify-between items-center">
       <span class="inline-flex items-center justify-center w-9 h-10 bg-white-50 rounded">
        <img class="h-9" src="/static/images/coins/{{ w.name }}.png" alt="{{ w.name }}">
       </span>
       <a class="py-2 px-3 bg-blue-500 text-xs text-white rounded-full hover:bg-blue-600" href="/wallet/{{ w.ticker }}">Manage Wallet</a>
      </div>
      <div class="px-6 mb-6">
       <h4 class="text-xl font-bold dark:text-white">{{ w.name }}
        <span class="inline-block font-medium text-xs text-gray-500 dark:text-white">({{ w.ticker }})</span>
       </h4>
       <p class="text-xs text-gray-500 dark:text-gray-200">Version: {{ w.version }} {% if w.updating %} <span class="hidden inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-700 dark:hover:bg-gray-700">Updating..</span></p>
       {% endif %}
      </div>
      <div class="p-6 bg-coolGray-100 dark:bg-gray-600">
          <div class="flex mb-2 justify-between items-center">
              <h4 class="text-xs font-medium dark:text-white">Balance:</h4>
              <div class="bold inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200 coinname-value" data-coinname="{{ w.name }}">{{ w.balance }} {{ w.ticker }}</div>
          </div>
          <div class="flex mb-2 justify-between items-center">
              <h4 class="text-xs font-medium dark:text-white ">{{ w.ticker }} USD value:</h4>
              <div class="bold inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200 usd-value" data-coinname="{{ w.name }}"></div>
          </div>
        {% if w.pending %}
         <div class="flex mb-2 justify-between items-center">
           <h4 class="text-xs font-bold text-green-500 dark:text-green-500">Pending:</h4>
           <span class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 coinname-value" data-coinname="{{ w.name }}">+{{ w.pending }} {{ w.ticker }}</span>
          </div>
         <div class="flex mb-2 justify-between items-center">
           <h4 class="text-xs font-bold text-green-500 dark:text-green-500">Pending USD value:</h4>
          <div class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 usd-value"></div>
         </div>
       {% endif %}
       {% if w.cid == '1' %} {# PART #}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-medium dark:text-white">Blind Balance:</h4>
        <span class="bold inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200 coinname-value" data-coinname="{{ w.name }}">{{ w.blind_balance }} {{ w.ticker }}</span>
       </div>
        <div class="flex mb-2 justify-between items-center">
          <h4 class="text-xs font-medium dark:text-white">Blind USD value:</h4>
          <div class="bold inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200 usd-value"></div>
        </div>
       {% if w.blind_unconfirmed %}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-bold text-green-500 dark:text-green-500">Blind Unconfirmed:</h4>
        <span class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 coinname-value" data-coinname="{{ w.name }}" >+{{ w.blind_unconfirmed }} {{ w.ticker }}</span>
       </div>
      <div class="flex mb-2 justify-between items-center">
          <h4 class="text-xs font-bold text-green-500 dark:text-green-500">Blind Unconfirmed USD value:</h4>
          <div class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 usd-value"></div>
        </div>
       {% endif %}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-medium dark:text-white">Anon Balance:</h4>
        <span class="bold inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200 coinname-value"  data-coinname="{{ w.name }}">{{ w.anon_balance }} {{ w.ticker }}</span>
       </div>
        <div class="flex mb-2 justify-between items-center">
          <h4 class="text-xs font-medium dark:text-white">Anon USD value:</h4>
          <div class="bold inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200 usd-value"></div>
        </div>
       {% if w.anon_pending %}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-bold text-green-500 dark:text-green-500">Anon Pending:</h4>
        <span class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 coinname-value" data-coinname="{{ w.name }}">
        +{{ w.anon_pending }} {{ w.ticker }}</span>
       </div>
        <div class="flex mb-2 justify-between items-center">
          <h4 class="text-xs font-bold text-green-500 dark:text-green-500">Anon Pending USD value:</h4>
          <div class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 usd-value"></div>
        </div>
       {% endif %}
       {% endif %} {# / PART #}
       {% if w.cid == '3' %} {# LTC #}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-medium dark:text-white">MWEB Balance:</h4>
        <span class="bold inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200 coinname-value" data-coinname="{{ w.name }}">{{ w.mweb_balance }} {{ w.ticker }}</span>
       </div>
        <div class="flex mb-2 justify-between items-center">
          <h4 class="text-xs font-medium dark:text-white">MWEB USD value:</h4>
          <div class="bold inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200 usd-value"></div>
        </div>
        {% if w.mweb_pending %}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-bold text-green-500 dark:text-green-500">MWEB Pending:</h4>
        <span class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 coinname-value"  data-coinname="{{ w.name }}">
        +{{ w.mweb_pending }} {{ w.ticker }}</span>
       </div>
        <div class="flex mb-2 justify-between items-center">
          <h4 class="text-xs font-bold text-green-500 dark:text-green-500">MWEB Pending USD value:</h4>
          <div class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 usd-value"></div>
        </div>
       {% endif %}
       {% endif %}
       {# / LTC #}
       <hr class="border-t border-gray-100 dark:border-gray-500 my-5">
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-medium dark:text-white">Blocks:</h4>
        <span class="inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200">{{ w.blocks }}{% if w.known_block_count %} / {{ w.known_block_count }}
        {% endif %}
      </span>
       </div>
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-medium dark:text-white">Last Updated:</h4>
        <span class="inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200">{{ w.lastupdated }}</span>
       </div>
       {% if w.bootstrapping %}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-medium dark:text-white">Bootstrapping:</h4>
        <span class="inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200">{{ w.bootstrapping }}</span>
       </div>
       {% endif %}
       {% if w.encrypted %}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-medium dark:text-white">Locked:</h4>
        <span class="inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200">{{ w.locked }}</span>
       </div>
       {% endif %}
       <div class="flex mb-2 justify-between items-center">
        <h4 class="text-xs font-medium dark:text-white">Expected Seed:</h4>
        <span class="inline-block py-1 px-2 rounded-full bg-blue-100 text-xs text-black-500 dark:bg-gray-500 dark:text-gray-200">{{ w.expected_seed }}</span>
       </div>
       <div class="flex justify-between mb-1 mt-10">
        <span class="text-xs font-medium dark:text-gray-200">Blockchain</span>
        <span class="text-xs font-medium dark:text-gray-200">{{ w.synced }}%</span>
       </div>
       <div class="w-full bg-gray-200 rounded-full h-1 " data-tooltip-target="tooltip-blocks{{loop.index}}">
        <div class="{% if w.synced | float < 100 %} bg-red-500 sync-bar-color-change {% else %} bg-blue-500 {% endif %} h-1 rounded-full" style="width: {{ w.synced }}%;"></div>
       </div>
        <div class="flex justify-between mb-1 mt-5">
        <span class="text-xs font-medium dark:text-gray-200">
    <script>
        if ({{ w.synced }} !== 100) {
            document.write("<p class='bg-gray-50 rounded overflow-hidden dark:bg-gray-500 p-2.5 dark:text-white'>The order book/blockchain is currently syncing, offers will only display once the network has fully <b>100%</b> synced. Please wait until the process completes.</p>");
        }
    </script>
      <div id="tooltip-blocks{{loop.index}}" role="tooltip" class="inline-block absolute invisible z-10 py-2 px-3 text-xs text-white {% if w.synced | float < 100 %} bg-red-500 sync-bar-color-change {% else %} bg-blue-500 {% endif %} rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip">
      <div><span class="bold">Blocks: {{ w.blocks }}{% if w.known_block_count %} / {{ w.known_block_count }} {% endif %}</div>
      <div class="tooltip-arrow pl-1" data-popper-arrow></div>
      </div>
        </span>
       </div>
      </div>
     </div>
     {% endif %}
     {% endif %}
    </div>
    {% endfor %}
   </div>
 </section>

{% include 'footer.html' %}

<script>
document.addEventListener('DOMContentLoaded', function() {

  if (window.WebSocketManager && typeof window.WebSocketManager.initialize === 'function') {
    window.WebSocketManager.initialize();
  }

  function setupWalletsWebSocketUpdates() {
    window.BalanceUpdatesManager.setup({
      contextKey: 'wallets',
      balanceUpdateCallback: updateWalletBalances,
      swapEventCallback: updateWalletBalances,
      errorContext: 'Wallets',
      enablePeriodicRefresh: true,
      periodicInterval: 60000
    });

    if (window.WebSocketManager && typeof window.WebSocketManager.addMessageHandler === 'function') {
      const priceHandlerId = window.WebSocketManager.addMessageHandler('message', (data) => {
        if (data && data.event) {
          if (data.event === 'price_updated' || data.event === 'prices_updated') {
            clearTimeout(window.walletsPriceUpdateTimeout);
            window.walletsPriceUpdateTimeout = setTimeout(() => {
              if (window.WalletManager && typeof window.WalletManager.updatePrices === 'function') {
                window.WalletManager.updatePrices(true);
              }
            }, 500);
          }
        }
      });
      window.walletsPriceHandlerId = priceHandlerId;
    }
  }

  function updateWalletBalances(balanceData) {
    if (balanceData) {
      balanceData.forEach(coin => {
        updateWalletDisplay(coin);
      });

      setTimeout(() => {
        if (window.WalletManager && typeof window.WalletManager.updatePrices === 'function') {
          window.WalletManager.updatePrices(true);
        }
      }, 250);
    } else {
      window.BalanceUpdatesManager.fetchBalanceData()
        .then(data => updateWalletBalances(data))
        .catch(error => {
          console.error('Error updating wallet balances:', error);
        });
    }
  }

  function updateWalletDisplay(coinData) {
    if (coinData.name === 'Particl') {
      updateSpecificBalance('Particl', 'Balance:', coinData.balance, coinData.ticker || 'PART');
    } else if (coinData.name === 'Particl Anon') {
      updateSpecificBalance('Particl', 'Anon Balance:', coinData.balance, coinData.ticker || 'PART');
      removePendingBalance('Particl', 'Anon Balance:');
      if (coinData.pending && parseFloat(coinData.pending) > 0) {
        updatePendingBalance('Particl', 'Anon Balance:', coinData.pending, coinData.ticker || 'PART', 'Anon Pending:', coinData);
      }
    } else if (coinData.name === 'Particl Blind') {
      updateSpecificBalance('Particl', 'Blind Balance:', coinData.balance, coinData.ticker || 'PART');
      removePendingBalance('Particl', 'Blind Balance:');
      if (coinData.pending && parseFloat(coinData.pending) > 0) {
        updatePendingBalance('Particl', 'Blind Balance:', coinData.pending, coinData.ticker || 'PART', 'Blind Unconfirmed:', coinData);
      }
    } else {
      updateSpecificBalance(coinData.name, 'Balance:', coinData.balance, coinData.ticker || coinData.name);

      if (coinData.name !== 'Particl Anon' && coinData.name !== 'Particl Blind' && coinData.name !== 'Litecoin MWEB') {
        if (coinData.pending && parseFloat(coinData.pending) > 0) {
          updatePendingDisplay(coinData);
        } else {
          removePendingDisplay(coinData.name);
        }
      }
    }
  }

  function updateSpecificBalance(coinName, labelText, balance, ticker, isPending = false) {
    const balanceElements = document.querySelectorAll('.coinname-value[data-coinname]');
    let found = false;

    balanceElements.forEach(element => {
      const elementCoinName = element.getAttribute('data-coinname');

      if (elementCoinName === coinName) {
        const parentDiv = element.closest('.flex.mb-2.justify-between.items-center');
        const labelElement = parentDiv ? parentDiv.querySelector('h4') : null;

        if (labelElement) {
          const currentLabel = labelElement.textContent.trim();

          if (currentLabel === labelText) {
            if (isPending) {
              const cleanBalance = balance.toString().replace(/^\+/, '');
              element.textContent = `+${cleanBalance} ${ticker}`;
            } else {
              element.textContent = `${balance} ${ticker}`;
            }
            found = true;
          }
        }
      }
    });

  }

  function updatePendingDisplay(coinData) {
    const walletContainer = findWalletContainer(coinData.name);
    if (!walletContainer) return;

    const existingPendingElements = walletContainer.querySelectorAll('.flex.mb-2.justify-between.items-center');
    let staticPendingElement = null;
    let staticUsdElement = null;

    existingPendingElements.forEach(element => {
      const labelElement = element.querySelector('h4');
      if (labelElement) {
        const labelText = labelElement.textContent;
        if (labelText.includes('Pending:') && !labelText.includes('USD')) {
          staticPendingElement = element;
        } else if (labelText.includes('Pending USD value:')) {
          staticUsdElement = element;
        }
      }
    });

    if (staticPendingElement && staticUsdElement) {
      const pendingSpan = staticPendingElement.querySelector('.coinname-value');
      if (pendingSpan) {
        const cleanPending = coinData.pending.toString().replace(/^\+/, '');
        pendingSpan.textContent = `+${cleanPending} ${coinData.ticker || coinData.name}`;
      }

      let initialUSD = '$0.00';
      if (window.WalletManager && window.WalletManager.coinPrices) {
        const coinId = coinData.name.toLowerCase().replace(' ', '-');
        const price = window.WalletManager.coinPrices[coinId];
        if (price && price.usd) {
          const cleanPending = coinData.pending.toString().replace(/^\+/, '');
          const usdValue = (parseFloat(cleanPending) * price.usd).toFixed(2);
          initialUSD = `$${usdValue}`;
        }
      }

      const usdDiv = staticUsdElement.querySelector('.usd-value');
      if (usdDiv) {
        usdDiv.textContent = initialUSD;
      }
      return;
    }

    let pendingContainer = walletContainer.querySelector('.pending-container');

    if (!pendingContainer) {
      pendingContainer = document.createElement('div');
      pendingContainer.className = 'pending-container';

      const pendingRow = document.createElement('div');
      pendingRow.className = 'flex mb-2 justify-between items-center';
      const cleanPending = coinData.pending.toString().replace(/^\+/, '');
      pendingRow.innerHTML = `
        <h4 class="text-xs font-bold text-green-500 dark:text-green-500">Pending:</h4>
        <span class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 coinname-value" data-coinname="${coinData.name}">+${cleanPending} ${coinData.ticker || coinData.name}</span>
      `;

      pendingContainer.appendChild(pendingRow);

      let initialUSD = '$0.00';
      if (window.WalletManager && window.WalletManager.coinPrices) {
        const coinId = coinData.name.toLowerCase().replace(' ', '-');
        const price = window.WalletManager.coinPrices[coinId];
        if (price && price.usd) {
          const usdValue = (parseFloat(cleanPending) * price.usd).toFixed(2);
          initialUSD = `$${usdValue}`;
        }
      }

      const usdRow = document.createElement('div');
      usdRow.className = 'flex mb-2 justify-between items-center';
      usdRow.innerHTML = `
        <h4 class="text-xs font-bold text-green-500 dark:text-green-500">Pending USD value:</h4>
        <div class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 usd-value">${initialUSD}</div>
      `;
      pendingContainer.appendChild(usdRow);

      const balanceRow = walletContainer.querySelector('.flex.mb-2.justify-between.items-center');
      let insertAfterElement = balanceRow;

      if (balanceRow) {
        let nextElement = balanceRow.nextElementSibling;
        while (nextElement) {
          const labelElement = nextElement.querySelector('h4');
          if (labelElement && labelElement.textContent.includes('USD value:') &&
              !labelElement.textContent.includes('Pending') && !labelElement.textContent.includes('Unconfirmed')) {
            insertAfterElement = nextElement;
            break;
          }
          nextElement = nextElement.nextElementSibling;
        }
      }

      if (insertAfterElement && insertAfterElement.nextSibling) {
        walletContainer.insertBefore(pendingContainer, insertAfterElement.nextSibling);
      } else {
        walletContainer.appendChild(pendingContainer);
      }
    } else {
      const pendingElement = pendingContainer.querySelector('.coinname-value');
      if (pendingElement) {
        const cleanPending = coinData.pending.toString().replace(/^\+/, ''); // Remove existing + if any
        pendingElement.textContent = `+${cleanPending} ${coinData.ticker || coinData.name}`;
      }
    }
  }

  function removePendingDisplay(coinName) {
    const walletContainer = findWalletContainer(coinName);
    if (!walletContainer) return;

    const pendingContainer = walletContainer.querySelector('.pending-container');
    if (pendingContainer) {
      pendingContainer.remove();
    }

    const existingPendingElements = walletContainer.querySelectorAll('.flex.mb-2.justify-between.items-center');
    existingPendingElements.forEach(element => {
      const labelElement = element.querySelector('h4');
      if (labelElement) {
        const labelText = labelElement.textContent;
        if (labelText.includes('Pending:') || labelText.includes('Pending USD value:')) {
          element.style.display = 'none';
        }
      }
    });
  }

  function removeSpecificPending(coinName, labelText) {
    const balanceElements = document.querySelectorAll('.coinname-value[data-coinname]');

    balanceElements.forEach(element => {
      const elementCoinName = element.getAttribute('data-coinname');

      if (elementCoinName === coinName) {
        const parentDiv = element.closest('.flex.mb-2.justify-between.items-center');
        const labelElement = parentDiv ? parentDiv.querySelector('h4') : null;

        if (labelElement) {
          const currentLabel = labelElement.textContent.trim();

          if (currentLabel === labelText) {
            parentDiv.remove();
          }
        }
      }
    });
  }

  function updatePendingBalance(coinName, balanceType, pendingAmount, ticker, pendingLabel, coinData) {
    const balanceElements = document.querySelectorAll('.coinname-value[data-coinname]');
    let targetElement = null;

    balanceElements.forEach(element => {
      const elementCoinName = element.getAttribute('data-coinname');
      if (elementCoinName === coinName) {
        const parentElement = element.closest('.flex.mb-2.justify-between.items-center');
        if (parentElement) {
          const labelElement = parentElement.querySelector('h4');
          if (labelElement && labelElement.textContent.includes(balanceType)) {
            targetElement = parentElement;
          }
        }
      }
    });

    if (!targetElement) return;

    let insertAfterElement = targetElement;
    let nextElement = targetElement.nextElementSibling;
    while (nextElement) {
      const labelElement = nextElement.querySelector('h4');
      if (labelElement) {
        const labelText = labelElement.textContent;
        if (labelText.includes('USD value:') && !labelText.includes('Pending') && !labelText.includes('Unconfirmed')) {
          insertAfterElement = nextElement;
          break;
        }
        if (labelText.includes('Balance:') || labelText.includes('Pending:') || labelText.includes('Unconfirmed:')) {
          break;
        }
      }
      nextElement = nextElement.nextElementSibling;
    }

    let pendingElement = insertAfterElement.nextElementSibling;
    while (pendingElement && !pendingElement.querySelector('h4')?.textContent.includes(pendingLabel)) {
      pendingElement = pendingElement.nextElementSibling;
      if (pendingElement && pendingElement.querySelector('h4')?.textContent.includes('Balance:')) {
        pendingElement = null;
        break;
      }
    }

    if (!pendingElement) {
      const newPendingElement = document.createElement('div');
      newPendingElement.className = 'flex mb-2 justify-between items-center';
      newPendingElement.innerHTML = `
        <h4 class="text-xs font-bold text-green-500 dark:text-green-500">${pendingLabel}</h4>
        <span class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 coinname-value" data-coinname="${coinName}">+${pendingAmount} ${ticker}</span>
      `;

      insertAfterElement.parentNode.insertBefore(newPendingElement, insertAfterElement.nextSibling);

      let initialUSD = '$0.00';
      if (window.WalletManager && window.WalletManager.coinPrices) {
        const coinId = coinName.toLowerCase().replace(' ', '-');
        const price = window.WalletManager.coinPrices[coinId];
        if (price && price.usd) {
          const usdValue = (parseFloat(pendingAmount) * price.usd).toFixed(2);
          initialUSD = `$${usdValue}`;
        }
      }

      const usdElement = document.createElement('div');
      usdElement.className = 'flex mb-2 justify-between items-center';
      usdElement.innerHTML = `
        <h4 class="text-xs font-bold text-green-500 dark:text-green-500">${pendingLabel.replace(':', '')} USD value:</h4>
        <div class="bold inline-block py-1 px-2 rounded-full bg-green-100 text-xs text-green-500 dark:bg-gray-500 dark:text-green-500 usd-value">${initialUSD}</div>
      `;

      newPendingElement.parentNode.insertBefore(usdElement, newPendingElement.nextSibling);
    } else {
      const pendingSpan = pendingElement.querySelector('.coinname-value');
      if (pendingSpan) {
        pendingSpan.textContent = `+${pendingAmount} ${ticker}`;
      }
    }
  }

  function removePendingBalance(coinName, balanceType) {
    const balanceElements = document.querySelectorAll('.coinname-value[data-coinname]');
    let targetElement = null;

    balanceElements.forEach(element => {
      const elementCoinName = element.getAttribute('data-coinname');
      if (elementCoinName === coinName) {
        const parentElement = element.closest('.flex.mb-2.justify-between.items-center');
        if (parentElement) {
          const labelElement = parentElement.querySelector('h4');
          if (labelElement && labelElement.textContent.includes(balanceType)) {
            targetElement = parentElement;
          }
        }
      }
    });

    if (!targetElement) return;

    let nextElement = targetElement.nextElementSibling;
    while (nextElement) {
      const labelElement = nextElement.querySelector('h4');
      if (labelElement) {
        const labelText = labelElement.textContent;
        if (labelText.includes('Pending:') || labelText.includes('Unconfirmed:') ||
            labelText.includes('Anon Pending:') || labelText.includes('Blind Unconfirmed:') ||
            labelText.includes('Pending USD value:') || labelText.includes('Unconfirmed USD value:') ||
            labelText.includes('Anon Pending USD value:') || labelText.includes('Blind Unconfirmed USD value:')) {
          const elementToRemove = nextElement;
          nextElement = nextElement.nextElementSibling;
          elementToRemove.remove();
        } else if (labelText.includes('Balance:')) {
          break; // Stop if we hit another balance
        } else {
          nextElement = nextElement.nextElementSibling;
        }
      } else {
        nextElement = nextElement.nextElementSibling;
      }
    }
  }

  function findWalletContainer(coinName) {
    const balanceElements = document.querySelectorAll('.coinname-value[data-coinname]');
    for (const element of balanceElements) {
      if (element.getAttribute('data-coinname') === coinName) {
        return element.closest('.bg-coolGray-100, .dark\\:bg-gray-600');
      }
    }
    return null;
  }

  function cleanupWalletsBalanceUpdates() {
    window.BalanceUpdatesManager.cleanup('wallets');

    if (window.walletsPriceHandlerId && window.WebSocketManager) {
      window.WebSocketManager.removeMessageHandler('message', window.walletsPriceHandlerId);
    }

    clearTimeout(window.walletsPriceUpdateTimeout);
  }

  window.BalanceUpdatesManager.initialize();
  setupWalletsWebSocketUpdates();

  setTimeout(() => {
    updateWalletBalances();
  }, 1000);

  if (window.CleanupManager) {
    window.CleanupManager.registerResource('walletsBalanceUpdates', null, cleanupWalletsBalanceUpdates);
  }

  window.addEventListener('beforeunload', cleanupWalletsBalanceUpdates);
});
</script>

</body>
</html>
