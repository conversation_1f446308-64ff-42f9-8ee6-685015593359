<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Fixed Features</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Test All Fixed Features</h1>
        
        <!-- Test 1: Notification Button with Badge (positioned under icon) -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">✅ 1. Notification Button with Badge (Dropdown Under Icon)</h2>
            <div class="relative inline-block">
                <button id="notification-history-button" onclick="toggleNotificationDropdown()" type="button"
                        class="relative text-gray-500 dark:text-gray-400 focus:outline-none rounded-lg text-sm ml-5">
                    <!-- Notification Icon -->
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                    </svg>
                    <!-- Notification Count Badge -->
                    <span id="notification-count-badge" class="absolute -top-1 -right-1 hidden bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">0</span>
                </button>
                <!-- Notification Dropdown (positioned under the notification icon) -->
                <div id="notification-history-dropdown" class="absolute right-0 top-full mt-2 z-50 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-80 dark:bg-gray-700 dark:divide-gray-600">
                    <div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                        No notifications yet
                    </div>
                </div>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                ✅ Badge shows count (red), icon turns blue, dropdown appears under icon
            </p>
        </div>

        <!-- Test 2: LTC/MWEB Donation with Type From Dropdown Integration -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">✅ 2. LTC/MWEB Donation with Type From Integration</h2>
            
            <!-- Simulated withdraw form -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Withdraw Address</label>
                    <input type="text" name="to_3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter LTC address">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type From:</label>
                    <select id="withdraw_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="plain">Plain</option>
                        <option value="mweb">MWEB</option>
                    </select>
                </div>
                
                <!-- Donation Section -->
                <div class="p-4 bg-coolGray-100 dark:bg-gray-500 border border-coolGray-200 dark:border-gray-400 rounded-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mt-1">
                            <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3 flex-1">
                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">Support BasicSwap Development!</h4>
                            <p class="text-sm text-gray-700 dark:text-gray-200 mb-3">
                                Consider donating Litecoin to help fund development.
                            </p>
                            
                            <!-- LTC with MWEB option -->
                            <div class="space-y-2">
                                <div class="flex flex-wrap gap-2">
                                    <button type="button" onclick="fillDonationAddress('ltc1qevlumv48nz2afl0re9ml4tdewc56svxq3egkyt', 'LTC')"
                                            class="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded-md transition-colors">
                                        Use LTC Address
                                    </button>
                                    <button type="button" onclick="fillDonationAddress('ltcmweb1qqt9rwznnxzkghv4s5wgtwxs0m0ry6n3atp95f47slppapxljde3xyqmdlnrc8ag7y2k354jzdc4pc4ks0kr43jehr77lngdecgh6689nn5mgv5yn', 'MWEB')"
                                            class="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white text-xs rounded-md transition-colors">
                                        Use MWEB Address
                                    </button>
                                </div>
                                <div class="text-xs text-gray-600 dark:text-gray-300">
                                    Current type: <span id="donation-type-indicator">LTC</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                ✅ Clicking buttons fills address AND updates "Type From:" dropdown
            </p>
        </div>

        <!-- Test Controls -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Test Controls</h2>
            <div class="space-x-4">
                <button onclick="addTestNotification()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                    Add Test Notification
                </button>
                <button onclick="clearNotifications()" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    Clear Notifications
                </button>
                <button onclick="toggleTheme()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                    Toggle Dark Mode
                </button>
            </div>
        </div>
    </div>

    <script>
        // Notification management
        let notificationCount = 0;
        
        function toggleNotificationDropdown() {
            const dropdown = document.getElementById('notification-history-dropdown');
            dropdown.classList.toggle('hidden');
        }
        
        function addTestNotification() {
            notificationCount++;
            updateNotificationBadge(notificationCount);
            
            // Update dropdown content
            const dropdown = document.getElementById('notification-history-dropdown');
            dropdown.innerHTML = `
                <div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer">
                    <div class="font-semibold">Test Notification ${notificationCount}</div>
                    <div class="text-xs">Balance change detected - Click to go to wallet</div>
                </div>
            `;
        }
        
        function clearNotifications() {
            notificationCount = 0;
            updateNotificationBadge(notificationCount);
            
            const dropdown = document.getElementById('notification-history-dropdown');
            dropdown.innerHTML = `
                <div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                    No notifications yet
                </div>
            `;
        }
        
        function updateNotificationBadge(count) {
            const badge = document.getElementById('notification-count-badge');
            const button = document.getElementById('notification-history-button');
            
            if (badge && button) {
                if (count > 0) {
                    badge.textContent = count > 99 ? '99+' : count.toString();
                    badge.classList.remove('hidden');
                    // Make button blue when there are notifications
                    button.classList.remove('text-gray-500', 'dark:text-gray-400');
                    button.classList.add('text-blue-500', 'dark:text-blue-400');
                } else {
                    badge.classList.add('hidden');
                    // Reset to normal color when no notifications
                    button.classList.remove('text-blue-500', 'dark:text-blue-400');
                    button.classList.add('text-gray-500', 'dark:text-gray-400');
                }
            }
        }
        
        // LTC/MWEB donation functionality
        function fillDonationAddress(address, type) {
            // Find the withdraw address input field
            const addressInput = document.querySelector('input[name^="to_"]');
            if (addressInput) {
                addressInput.value = address;
                addressInput.focus();
                
                // Update type indicator if it exists
                const typeIndicator = document.getElementById('donation-type-indicator');
                if (typeIndicator && type) {
                    typeIndicator.textContent = type;
                }
                
                // Update the "Type From:" dropdown for LTC
                const withdrawTypeSelect = document.getElementById('withdraw_type');
                if (withdrawTypeSelect && type) {
                    if (type === 'LTC') {
                        withdrawTypeSelect.value = 'plain';
                    } else if (type === 'MWEB') {
                        withdrawTypeSelect.value = 'mweb';
                    }
                }
                
                // Show feedback
                const feedback = document.createElement('div');
                feedback.className = 'fixed z-50 bg-green-600 text-white text-sm py-2 px-3 rounded-md shadow-lg';
                feedback.innerText = `✅ ${type} address filled and Type From updated to ${type === 'LTC' ? 'Plain' : 'MWEB'}!`;
                feedback.style.top = '20px';
                feedback.style.right = '20px';
                feedback.style.opacity = '0';
                feedback.style.transition = 'opacity 0.3s ease-in-out';
                
                document.body.appendChild(feedback);
                
                setTimeout(() => {
                    feedback.style.opacity = '1';
                }, 10);
                
                setTimeout(() => {
                    feedback.style.opacity = '0';
                    setTimeout(() => {
                        if (feedback.parentNode) {
                            feedback.parentNode.removeChild(feedback);
                        }
                    }, 300);
                }, 3000);
            } else {
                alert('Address input field not found!');
            }
        }
        
        // Theme toggle
        function toggleTheme() {
            document.documentElement.classList.toggle('dark');
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const button = document.getElementById('notification-history-button');
            const dropdown = document.getElementById('notification-history-dropdown');
            
            if (dropdown && !dropdown.contains(event.target) && !button.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
