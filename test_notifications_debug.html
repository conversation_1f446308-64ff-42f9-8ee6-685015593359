<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Notification Issues</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Debug Notification Issues</h1>
        
        <!-- Test Notification Button with Proper Alignment -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">🔧 1. Notification Icon Alignment Test</h2>
            <div class="bg-gray-800 p-4 rounded-lg flex items-center space-x-4">
                <!-- Simulated header icons -->
                <span class="text-gray-400 text-sm">Other Icons:</span>
                
                <!-- Settings icon -->
                <button class="text-gray-500 dark:text-gray-400 focus:outline-none rounded-lg text-sm ml-5">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                    </svg>
                </button>
                
                <!-- Notification icon (FIXED) -->
                <div class="relative">
                    <button id="notification-history-button" onclick="toggleNotificationDropdown()" type="button"
                            class="text-gray-500 dark:text-gray-400 focus:outline-none rounded-lg text-sm ml-5">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                        </svg>
                        <span id="notification-count-badge" class="absolute -top-1 -right-1 hidden bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">0</span>
                    </button>
                    <!-- Dropdown CENTERED under icon -->
                    <div id="notification-history-dropdown" class="absolute left-1/2 transform -translate-x-1/2 top-full mt-2 z-50 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-80 dark:bg-gray-700 dark:divide-gray-600">
                        <div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                            No notifications yet
                        </div>
                    </div>
                </div>
                
                <!-- Theme toggle -->
                <button class="text-gray-500 dark:text-gray-400 focus:outline-none rounded-lg text-sm ml-5">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                ✅ Icons should be aligned horizontally, dropdown should be centered under notification icon
            </p>
        </div>

        <!-- Test Notification System -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">🧪 2. Notification System Test</h2>
            <div class="space-y-4">
                <div class="space-x-4">
                    <button onclick="testCreateToast()" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        Test Create Toast
                    </button>
                    <button onclick="testWithdrawalNotification()" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                        Test Withdrawal Notification
                    </button>
                    <button onclick="testIncomingNotification()" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        Test Incoming Notification
                    </button>
                    <button onclick="clearAllNotifications()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                        Clear All
                    </button>
                </div>
                
                <div class="bg-gray-800 p-4 rounded-lg">
                    <h3 class="text-white font-semibold mb-2">Debug Info:</h3>
                    <div id="debug-info" class="text-green-400 text-sm font-mono"></div>
                </div>
            </div>
        </div>

        <!-- Test Dropdown Centering -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">📍 3. Dropdown Centering Options</h2>
            <div class="space-y-4">
                <div class="flex items-center space-x-8">
                    <div class="text-center">
                        <p class="text-sm mb-2">Option 1: left-1/2 transform -translate-x-1/2</p>
                        <div class="relative inline-block">
                            <button class="bg-blue-500 text-white px-4 py-2 rounded">Button</button>
                            <div class="absolute left-1/2 transform -translate-x-1/2 top-full mt-2 bg-white border rounded shadow p-4 w-64">
                                Centered dropdown
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <p class="text-sm mb-2">Option 2: -right-32 (current)</p>
                        <div class="relative inline-block">
                            <button class="bg-red-500 text-white px-4 py-2 rounded">Button</button>
                            <div class="absolute -right-32 top-full mt-2 bg-white border rounded shadow p-4 w-64">
                                Right offset dropdown
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let notificationCount = 0;
        let notificationHistory = [];
        
        function toggleNotificationDropdown() {
            const dropdown = document.getElementById('notification-history-dropdown');
            if (dropdown) {
                dropdown.classList.toggle('hidden');
                updateDebugInfo('Dropdown toggled');
            } else {
                updateDebugInfo('ERROR: Dropdown element not found');
            }
        }
        
        function updateNotificationBadge(count) {
            const badge = document.getElementById('notification-count-badge');
            const button = document.getElementById('notification-history-button');
            
            if (badge && button) {
                if (count > 0) {
                    badge.textContent = count > 99 ? '99+' : count.toString();
                    badge.classList.remove('hidden');
                    button.classList.remove('text-gray-500', 'dark:text-gray-400');
                    button.classList.add('text-blue-500', 'dark:text-blue-400');
                } else {
                    badge.classList.add('hidden');
                    button.classList.remove('text-blue-500', 'dark:text-blue-400');
                    button.classList.add('text-gray-500', 'dark:text-gray-400');
                }
                updateDebugInfo(`Badge updated: count=${count}, visible=${count > 0}`);
            } else {
                updateDebugInfo('ERROR: Badge or button element not found');
            }
        }
        
        function updateNotificationDropdown() {
            const dropdown = document.getElementById('notification-history-dropdown');
            if (!dropdown) {
                updateDebugInfo('ERROR: Dropdown element not found');
                return;
            }
            
            if (notificationHistory.length === 0) {
                dropdown.innerHTML = '<div class="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">No notifications yet</div>';
                updateDebugInfo('Dropdown updated: empty state');
                return;
            }
            
            let historyHTML = '';
            notificationHistory.forEach((item, index) => {
                historyHTML += `
                    <div class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-600 border-b border-gray-100 dark:border-gray-600 last:border-b-0 cursor-pointer transition-colors">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6z"/>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">${item.title}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">${item.subtitle}</div>
                                <div class="text-xs text-gray-400 dark:text-gray-500">${item.timestamp}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            dropdown.innerHTML = historyHTML;
            updateDebugInfo(`Dropdown updated: ${notificationHistory.length} items`);
        }
        
        function testCreateToast() {
            notificationCount++;
            const notification = {
                title: `Test Toast ${notificationCount}`,
                subtitle: 'This is a test notification',
                timestamp: new Date().toLocaleTimeString(),
                type: 'success'
            };
            
            notificationHistory.unshift(notification);
            if (notificationHistory.length > 10) {
                notificationHistory = notificationHistory.slice(0, 10);
            }
            
            updateNotificationBadge(notificationHistory.length);
            updateNotificationDropdown();
            updateDebugInfo(`Created test toast #${notificationCount}`);
        }
        
        function testWithdrawalNotification() {
            notificationCount++;
            const notification = {
                title: '-0.05000000 PART',
                subtitle: 'Funds sent',
                timestamp: new Date().toLocaleTimeString(),
                type: 'balance_change'
            };
            
            notificationHistory.unshift(notification);
            if (notificationHistory.length > 10) {
                notificationHistory = notificationHistory.slice(0, 10);
            }
            
            updateNotificationBadge(notificationHistory.length);
            updateNotificationDropdown();
            updateDebugInfo(`Created withdrawal notification: ${notification.title}`);
        }
        
        function testIncomingNotification() {
            notificationCount++;
            const notification = {
                title: '+0.12345678 PART',
                subtitle: 'Incoming funds confirmed',
                timestamp: new Date().toLocaleTimeString(),
                type: 'balance_change'
            };
            
            notificationHistory.unshift(notification);
            if (notificationHistory.length > 10) {
                notificationHistory = notificationHistory.slice(0, 10);
            }
            
            updateNotificationBadge(notificationHistory.length);
            updateNotificationDropdown();
            updateDebugInfo(`Created incoming notification: ${notification.title}`);
        }
        
        function clearAllNotifications() {
            notificationHistory = [];
            notificationCount = 0;
            updateNotificationBadge(0);
            updateNotificationDropdown();
            updateDebugInfo('All notifications cleared');
        }
        
        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('Page loaded, notification system ready');
            updateNotificationBadge(0);
            updateNotificationDropdown();
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const button = document.getElementById('notification-history-button');
            const dropdown = document.getElementById('notification-history-dropdown');
            
            if (dropdown && !dropdown.contains(event.target) && !button.contains(event.target)) {
                dropdown.classList.add('hidden');
                updateDebugInfo('Dropdown closed (clicked outside)');
            }
        });
    </script>
</body>
</html>
